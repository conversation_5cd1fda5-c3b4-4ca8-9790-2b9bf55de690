"""
URL Data Retrieval Service for Entity Extraction Tool

This service retrieves URLs and their classification data from the database
without re-running the classifiers.
"""

import json
from typing import Dict, List, Optional, Tuple, Any
from sqlmodel import Session, select
from urllib.parse import urlparse
from app.database import get_session
from app.models.db_models import WebsiteUrls, MccUrlClassification, PolicyUrlClassification, PolicyAnalysisNew, engine
from app.Extractor.utils.logger import EntityExtractorLogger

def _normalize_url_path(url: str) -> str:
    """
    Normalize URL for comparison by extracting and cleaning the path

    Args:
        url: Full URL to normalize

    Returns:
        Normalized path for comparison
    """
    if not url:
        return ""

    # Handle special cases
    if url == "not_found":
        return "not_found"

    try:
        parsed = urlparse(url)
        path = parsed.path.strip("/").lower()

        # If path is empty (root URL), use a special identifier
        if not path:
            return "home_page"

        return path
    except Exception:
        # Fallback to original URL if parsing fails
        return url.lower()
class UrlDataRetrievalService:
    """
    Service to retrieve URL data and classification results from database
    """

    def __init__(self, scrape_request_ref_id: str, org_id: str = "default"):
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        from app.Extractor.utils.logger import EntityExtractorLogger
        self.logger = EntityExtractorLogger(
            analysis_id=f"url_retrieval_{scrape_request_ref_id}",
            scrape_request_id=scrape_request_ref_id
        )

    @staticmethod
    def find_scrape_request_ref_id_by_website(website_url: str, org_id: str = "default") -> Optional[str]:
        """
        Find the most recent scrape_request_ref_id for a given website URL

        Args:
            website_url: Main website URL
            org_id: Organization ID

        Returns:
            Most recent scrape_request_ref_id or None if not found
        """
        try:
            logger = EntityExtractorLogger(
                analysis_id="website_lookup",
                scrape_request_id="lookup"
            )

            logger.info(f"Looking up scrape_request_ref_id for website: {website_url}")

            with Session(engine) as session:
                # Try to find by exact website match first
                exact_query = select(WebsiteUrls).where(
                    WebsiteUrls.website == website_url,
                    WebsiteUrls.org_id == org_id
                ).order_by(WebsiteUrls.id.desc())  # Most recent first

                exact_result = session.exec(exact_query).first()

                if exact_result:
                    logger.info(f"Found exact match: {exact_result.scrape_request_ref_id}")
                    return exact_result.scrape_request_ref_id

                # If no exact match, try partial match (domain-based)
                from urllib.parse import urlparse
                parsed_url = urlparse(website_url)
                domain = parsed_url.netloc.lower()

                if domain:
                    # Remove www. if present
                    if domain.startswith('www.'):
                        domain = domain[4:]

                    partial_query = select(WebsiteUrls).where(
                        WebsiteUrls.website.contains(domain),
                        WebsiteUrls.org_id == org_id
                    ).order_by(WebsiteUrls.id.desc())

                    partial_result = session.exec(partial_query).first()

                    if partial_result:
                        logger.info(f"Found partial match: {partial_result.scrape_request_ref_id} for domain: {domain}")
                        return partial_result.scrape_request_ref_id

                logger.warning(f"No scrape_request_ref_id found for website: {website_url}")
                return None

        except Exception as e:
            logger = EntityExtractorLogger(
                analysis_id="website_lookup_error",
                scrape_request_id="lookup"
            )
            logger.error(f"Error finding scrape_request_ref_id for website {website_url}", error=e)
            return None
    
    def get_policy_urls_with_reachability(self, website_url: str = None) -> Dict[str, Dict[str, Any]]:
        """
        Get policy URLs (home_page, about_us, terms_and_condition, contact_us, privacy_policy, shipping_delivery)
        with their reachability status and classification details

        Args:
            website_url: Optional website URL for fallback when no scrape data exists

        Returns:
            Dictionary with policy types as keys and URL details as values
        """
        try:
            self.logger.info("Retrieving policy URLs with reachability status")

            policy_urls = {}

            # Step 1: Get URLs from MCC classification table with latest scrape_request_ref_id
            mcc_policy_urls = self._get_mcc_policy_urls_latest()
            if mcc_policy_urls:
                policy_urls.update(mcc_policy_urls)
                self.logger.info(f"Retrieved {len(mcc_policy_urls)} policy URLs from MCC classification")

            # Step 2: Check for unreachable URLs and get from policy_analysis_new_gemini
            unreachable_urls = self._get_unreachable_policy_urls(policy_urls)
            if unreachable_urls:
                policy_analysis_data = self._get_policy_analysis_data()
                if policy_analysis_data:
                    # Update unreachable URLs with policy analysis data
                    for policy_type, url_data in unreachable_urls.items():
                        if policy_type in policy_analysis_data:
                            url_data.update(policy_analysis_data[policy_type])
                            url_data['reachable_by_gemini'] = False
                            url_data['data_source'] = 'policy_analysis_new_gemini'
                            policy_urls[policy_type] = url_data

                self.logger.info(f"Retrieved data for {len(unreachable_urls)} unreachable URLs from policy analysis table")

            # Step 3: NEW LOGIC - If MCC only provides home page, force-load remaining policies from policy_analysis_new_gemini
            main_policy_types = ["privacy_policy", "terms_and_condition", "shipping_delivery", "contact_us", "about_us"]
            mcc_policy_types = set(mcc_policy_urls.keys()) if mcc_policy_urls else set()

            # Check if MCC only has home_page (or very few policy types)
            missing_policy_types = [pt for pt in main_policy_types if pt not in mcc_policy_types]

            if missing_policy_types and len(mcc_policy_types) <= 1:  # MCC only has home_page or nothing
                self.logger.info(f"MCC only provided {len(mcc_policy_types)} policy types, force-loading {len(missing_policy_types)} missing policies from policy_analysis_new_gemini")

                policy_analysis_data = self._get_policy_analysis_data()
                if policy_analysis_data:
                    for policy_type in missing_policy_types:
                        if policy_type in policy_analysis_data and policy_type not in policy_urls:
                            # Force-load as unreachable (route to OpenAI)
                            policy_urls[policy_type] = {
                                'url': policy_analysis_data[policy_type].get('original_url'),
                                'original_url': policy_analysis_data[policy_type].get('original_url'),
                                'hard_classification': policy_type,
                                'soft_classification': policy_type,
                                'final_classification': policy_type,
                                'reachable_by_gemini': False,  # Force OpenAI analysis
                                'data_source': 'policy_analysis_new_gemini_forced',
                                'classification_confidence': None,
                                'extracted_text': policy_analysis_data[policy_type].get('extracted_text'),
                                'screenshot_url': policy_analysis_data[policy_type].get('screenshot_url')
                            }

                    self.logger.info(f"Force-loaded {len([pt for pt in missing_policy_types if pt in policy_analysis_data])} policies from policy_analysis_new_gemini as unreachable")
            
            # Fallback: If no policy URLs found and website_url provided, create basic home page entry
            if not policy_urls and website_url:
                self.logger.info(f"No policy URLs found for scrape_request_ref_id, creating fallback entry for website: {website_url}")
                policy_urls['home_page'] = {
                    'url': website_url,
                    'original_url': website_url,
                    'hard_classification': 'home_page',
                    'soft_classification': 'home_page',
                    'final_classification': 'home_page',
                    'reachable_by_gemini': True,  # Assume reachable for fallback
                    'data_source': 'fallback_website_url',
                    'classification_confidence': None
                }

            self.logger.info(f"Total policy URLs retrieved: {len(policy_urls)}")
            policy_analysis_data = self._get_policy_analysis_data()
            for required_policy in ["privacy_policy", "terms_and_condition"]:
                if required_policy not in policy_urls and required_policy in policy_analysis_data:
                    policy_urls[required_policy] = policy_analysis_data[required_policy]
                    policy_urls[required_policy]['reachable_by_gemini'] = False  # Force OpenAI fallback
                    policy_urls[required_policy]['data_source'] = 'policy_analysis_new_gemini_forced'
                    self.logger.info(f"Forced inclusion of {required_policy} from policy_analysis_new_gemini")
            return policy_urls

        except Exception as e:
            self.logger.error("Error retrieving policy URLs with reachability", error=e)
            # Fallback: If error and website_url provided, create basic home page entry
            if website_url:
                self.logger.info(f"Error occurred but creating fallback entry for website: {website_url}")
                return {
                    'home_page': {
                        'url': website_url,
                        'original_url': website_url,
                        'hard_classification': 'home_page',
                        'soft_classification': 'home_page',
                        'final_classification': 'home_page',
                        'reachable_by_gemini': True,
                        'data_source': 'fallback_website_url',
                        'classification_confidence': None
                    }
                }
            return {}

    def get_extracted_text_for_url(self, url: str) -> Optional[str]:
        """
        Get extracted text content for a specific URL

        Args:
            url: The URL to get text for

        Returns:
            Extracted text content or None if not found
        """
        try:
            # Debug: Check if url is actually a string
            if not isinstance(url, str):
                self.logger.error(f"get_extracted_text_for_url received non-string parameter: {type(url)} - {url}")
                # If it's a dictionary with 'url' key, extract it
                if isinstance(url, dict) and 'url' in url:
                    actual_url = url['url']
                    self.logger.warning(f"Extracted URL from dictionary: {actual_url}")
                    url = actual_url
                else:
                    self.logger.error(f"Cannot extract URL from parameter: {url}")
                    return None
            # First try to get text from policy_analysis_new_gemini table
            text_content = self._get_text_from_policy_table(url)
            if text_content:
                self.logger.info(f"Retrieved extracted text for URL from policy table: {url}")
                return text_content

            # Fallback to WebsiteUrls table
            with Session(engine) as session:
                url_query = select(WebsiteUrls).where(
                    WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id,
                    WebsiteUrls.url == url
                )
                url_record = session.exec(url_query).first()

                if url_record and url_record.extracted_text:
                    self.logger.info(f"Retrieved extracted text for URL from WebsiteUrls: {url}")
                    return url_record.extracted_text
                else:
                    self.logger.warning(f"No extracted text found for URL: {url}")
                    return None

        except Exception as e:
            self.logger.error(f"Error retrieving extracted text for URL {url}", error=e)
            return None

    def check_gemini_reachability(self, urls: List[str]) -> Tuple[List[str], List[str]]:
        """
        Check which URLs are reachable by Gemini

        Note: Since the risky classification service was removed, this implementation
        uses a simple approach that treats all URLs as reachable by Gemini first,
        with fallback to OpenAI if Gemini fails during actual processing.

        Args:
            urls: List of URLs to check

        Returns:
            Tuple of (reachable_urls, unreachable_urls)
        """
        try:
            self.logger.info(f"Checking Gemini reachability for {len(urls)} URLs")

            # Simple implementation: assume all URLs are reachable by Gemini
            # The actual reachability will be determined during Gemini processing
            # If Gemini fails, the orchestrator will fall back to OpenAI
            reachable_urls = urls.copy()
            unreachable_urls = []

            self.logger.info(f"Gemini reachability check completed: {len(reachable_urls)} reachable, {len(unreachable_urls)} unreachable")

            return reachable_urls, unreachable_urls

        except Exception as e:
            self.logger.error("Error checking Gemini reachability", error=e)
            # Return all URLs as unreachable on error (fallback to OpenAI)
            return [], urls

    def _get_mcc_policy_urls_latest(self) -> Dict[str, Dict[str, Any]]:
        """Get policy URLs from MCC classification table with latest scrape_request_ref_id"""
        try:
            with Session(engine) as session:
                # First, get the latest scrape_request_ref_id for this website
                latest_ref_id = self._get_latest_scrape_request_ref_id()
                if not latest_ref_id:
                    self.logger.warning("No latest scrape_request_ref_id found")
                    return {}

                mcc_query = select(MccUrlClassification).where(
                    MccUrlClassification.scrape_request_ref_id == latest_ref_id
                )
                mcc_records = session.exec(mcc_query).all()

                policy_urls = {}
                policy_categories = [
                    'home_page', 'about_us', 'terms_and_condition', 'contact_us', 
                    'privacy_policy', 'shipping_delivery', 'returns_cancellation_exchange'
                ]
                
                for record in mcc_records:
                    if record.hard_classification and record.url:
                        # Check if this URL is classified as a policy page
                        hard_class = record.hard_classification.lower()
                        final_class = record.final_classification.lower() if record.final_classification else ""
                        
                        for policy_type in policy_categories:
                            if policy_type in hard_class or policy_type in final_class:
                                # Check if URL is reachable (not marked as "unreachable via tool")
                                is_reachable = not ("unreachable" in hard_class or "unreachable" in final_class)
                                
                                policy_urls[policy_type] = {
                                    'url': record.url,
                                    'hard_classification': record.hard_classification,
                                    'final_classification': record.final_classification,
                                    'soft_classification': record.soft_classification,
                                    'reachable_by_gemini': is_reachable,
                                    'data_source': 'mcc_url_classification_gemini',
                                    'classification_confidence': getattr(record, 'classification_confidence', None)
                                }
                                break

                return policy_urls

        except Exception as e:
            self.logger.error("Error retrieving MCC policy URLs", error=e)
            return {}

    def _get_latest_scrape_request_ref_id(self) -> Optional[str]:
        """Get the latest scrape_request_ref_id for the current website"""
        try:
            with Session(engine) as session:
                # Get the website URL from the current scrape_request_ref_id
                current_query = select(MccUrlClassification).where(
                    MccUrlClassification.scrape_request_ref_id == self.scrape_request_ref_id
                ).limit(1)
                current_record = session.exec(current_query).first()
                
                if not current_record:
                    return self.scrape_request_ref_id
                
                # Extract domain from the current record's URL
                from urllib.parse import urlparse
                parsed_url = urlparse(current_record.url)
                domain = parsed_url.netloc.lower()
                
                if domain.startswith('www.'):
                    domain = domain[4:]
                
                # Find all scrape_request_ref_ids for this domain, ordered by latest
                all_refs_query = select(MccUrlClassification).where(
                    MccUrlClassification.url.contains(domain)
                ).order_by(MccUrlClassification.mcc_analysis_id.desc())
                
                all_records = session.exec(all_refs_query).all()
                
                if all_records:
                    # Get the latest scrape_request_ref_id
                    latest_ref_id = all_records[0].scrape_request_ref_id
                    self.logger.info(f"Found latest scrape_request_ref_id: {latest_ref_id} for domain: {domain}")
                    return latest_ref_id
                
                return self.scrape_request_ref_id

        except Exception as e:
            self.logger.error("Error getting latest scrape_request_ref_id", error=e)
            return self.scrape_request_ref_id

    def _get_unreachable_policy_urls(self, existing_policy_urls: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """Get URLs that are marked as unreachable"""
        unreachable_urls = {}
        
        for policy_type, url_data in existing_policy_urls.items():
            if not url_data.get('reachable_by_gemini', True):
                unreachable_urls[policy_type] = url_data
        
        return unreachable_urls

    def _get_policy_analysis_data(self) -> Dict[str, Dict[str, Any]]:
        """Get policy data from policy_analysis_new_gemini table with text priority"""
        try:
            with Session(engine) as session:
                # Try to find policy analysis record for the latest scrape_request_ref_id
                latest_ref_id = self._get_latest_scrape_request_ref_id()
                
                policy_query = select(PolicyAnalysisNew).where(
                    PolicyAnalysisNew.scrape_request_ref_id == latest_ref_id
                )
                policy_record = session.exec(policy_query).first()

                if not policy_record:
                    # Fallback to current scrape_request_ref_id
                    policy_query = select(PolicyAnalysisNew).where(
                        PolicyAnalysisNew.scrape_request_ref_id == self.scrape_request_ref_id
                    )
                    policy_record = session.exec(policy_query).first()

                if not policy_record:
                    return {}

                policy_data = {}
                
                # Map policy types to their corresponding fields in the table
                # Prioritize text over screenshots for entity extraction
                policy_mappings = {
                    'home_page': {
                        'url': policy_record.home_page_url,
                        'text': policy_record.home_page_text,
                        'screenshot': policy_record.home_page_screenshot
                    },
                    'about_us': {
                        'url': policy_record.about_us_url,
                        'text': policy_record.about_us_text,
                        'screenshot': policy_record.about_us_screenshot
                    },
                    'terms_and_condition': {
                        'url': policy_record.terms_and_condition_url,
                        'text': policy_record.terms_and_condition_text,
                        'screenshot': policy_record.terms_and_condition_screenshot
                    },
                    'contact_us': {
                        'url': policy_record.contact_us_url,
                        'text': policy_record.contact_us_text,
                        'screenshot': policy_record.contact_us_screenshot
                    },
                    'privacy_policy': {
                        'url': policy_record.privacy_policy_url,
                        'text': policy_record.privacy_policy_text,
                        'screenshot': policy_record.privacy_policy_screenshot
                    },
                    'shipping_delivery': {
                        'url': policy_record.shipping_delivery_url,
                        'text': policy_record.shipping_delivery_text,
                        'screenshot': policy_record.shipping_delivery_screenshot
                    },
                    'returns_cancellation_exchange': {
                        'url': policy_record.returns_cancellation_exchange_url,
                        'text': policy_record.returns_cancellation_exchange_text,
                        'screenshot': policy_record.returns_cancellation_exchange_screenshot
                    }
                }
                
                # Only include policies that have data, prioritizing text content
                for policy_type, data in policy_mappings.items():
                    if data['url'] or data['text']:
                        policy_data[policy_type] = {
                            'extracted_text': data['text'],  # Text takes priority
                            'screenshot_url': data['screenshot'],  # Screenshot as backup
                            'original_url': data['url']
                        }

                self.logger.info(f"Retrieved policy analysis data for {len(policy_data)} policy types")
                return policy_data

        except Exception as e:
            self.logger.error("Error retrieving policy analysis data", error=e)
            return {}

    # def _get_text_from_policy_table(self, url: str) -> Optional[str]:
    #     """
    #     Get extracted text from policy_analysis_new_gemini table for a specific URL
        
    #     Args:
    #         url: The URL to get text for
            
    #     Returns:
    #         Extracted text content or None if not found
    #     """
    #     try:
    #         from app.models.db_models import PolicyAnalysisNew
    #         from app.database import get_session
    #         from sqlmodel import select

    #         with next(get_session()) as session:
    #             # Query the policy analysis new table
    #             query = select(PolicyAnalysisNew).where(
    #                 PolicyAnalysisNew.scrape_request_ref_id == self.scrape_request_ref_id
    #             )
    #             policy_record = session.exec(query).first()

    #             if not policy_record:
    #                 return None

    #             # Map URLs to their corresponding text fields
    #             url_to_text_mapping = {
    #                 policy_record.home_page_url: policy_record.home_page_text,
    #                 policy_record.terms_and_condition_url: policy_record.terms_and_condition_text,
    #                 policy_record.privacy_policy_url: policy_record.privacy_policy_text,
    #                 policy_record.shipping_delivery_url: policy_record.shipping_delivery_text,
    #                 policy_record.contact_us_url: policy_record.contact_us_text,
    #                 policy_record.about_us_url: policy_record.about_us_text,
    #                 policy_record.returns_cancellation_exchange_url: policy_record.returns_cancellation_exchange_text
    #             }

    #             # Find matching URL and return its text
    #             for stored_url, text_content in url_to_text_mapping.items():
    #                 if stored_url and stored_url.strip() == url.strip() and text_content:
    #                     return text_content.strip()

    #             return None

    #     except Exception as e:
    #         self.logger.error(f"Error retrieving text from policy table for URL {url}", error=e)
    #         return None
    
    
    
    def _get_text_from_policy_table(self, url: str) -> Optional[str]:
        try:
            from app.models.db_models import PolicyAnalysisNew
            from app.database import get_session
            from sqlmodel import select
            from urllib.parse import urlparse

            normalized_input = _normalize_url_path(url)
            self.logger.info(f"[URL_MATCH_DEBUG] Looking for text for URL: {url}")
            self.logger.info(f"[URL_MATCH_DEBUG] Normalized input: '{normalized_input}'")

            with next(get_session()) as session:
                query = select(PolicyAnalysisNew).where(
                    PolicyAnalysisNew.scrape_request_ref_id == self.scrape_request_ref_id
                )
                policy_record = session.exec(query).first()

                if not policy_record:
                    self.logger.warning(f"[URL_MATCH_DEBUG] No policy record found for scrape_request_ref_id: {self.scrape_request_ref_id}")
                    return None

                # Enhanced URL mapping with better field names
                url_text_map = [
                    ("home_page", policy_record.home_page_url, policy_record.home_page_text),
                    ("terms_and_condition", policy_record.terms_and_condition_url, policy_record.terms_and_condition_text),
                    ("privacy_policy", policy_record.privacy_policy_url, policy_record.privacy_policy_text),
                    ("shipping_delivery", policy_record.shipping_delivery_url, policy_record.shipping_delivery_text),
                    ("contact_us", policy_record.contact_us_url, policy_record.contact_us_text),
                    ("about_us", policy_record.about_us_url, policy_record.about_us_text),
                    ("returns_cancellation", policy_record.returns_cancellation_exchange_url, policy_record.returns_cancellation_exchange_text)
                ]

                self.logger.info(f"[URL_MATCH_DEBUG] Available URLs in database:")
                for policy_type, stored_url, text in url_text_map:
                    if stored_url:
                        text_length = len(text) if text else 0
                        norm_stored = _normalize_url_path(stored_url)
                        self.logger.info(f"[URL_MATCH_DEBUG]   [{policy_type}] {stored_url} -> '{norm_stored}' (text: {text_length} chars)")

                # Enhanced matching logic with multiple strategies
                for policy_type, stored_url, text in url_text_map:
                    if not stored_url or not text:
                        continue

                    norm_stored = _normalize_url_path(stored_url)

                    # Strategy 1: Exact match
                    if norm_stored == normalized_input:
                        self.logger.info(f"[EXACT_MATCH] Input URL: {url} <-> [{policy_type}] {stored_url} (text: {len(text)} chars)")
                        return text.strip()

                    # Strategy 2: Path-based fuzzy matching for policy URLs
                    if self._fuzzy_path_match(normalized_input, norm_stored, policy_type):
                        self.logger.info(f"[FUZZY_MATCH] Input URL: {url} <-> [{policy_type}] {stored_url} (text: {len(text)} chars)")
                        return text.strip()

                self.logger.warning(f"[NO_MATCH] Could not match input URL: {url} (normalized: '{normalized_input}') to any stored policy URL")
                return None

        except Exception as e:
            self.logger.error(f"Error retrieving text from policy table for URL {url}", error=e)
            return None

    def _fuzzy_path_match(self, input_path: str, stored_path: str, policy_type: str) -> bool:
        """
        Enhanced fuzzy matching for policy URLs

        Args:
            input_path: Normalized input URL path
            stored_path: Normalized stored URL path
            policy_type: Type of policy (privacy_policy, terms_and_condition, etc.)

        Returns:
            True if paths match with fuzzy logic
        """
        try:
            # Skip home page fuzzy matching to avoid false positives
            if input_path == "home_page" or stored_path == "home_page":
                return False

            # Skip if either path is too short
            if len(input_path) < 3 or len(stored_path) < 3:
                return False

            # Strategy 1: Substring matching (either direction)
            if input_path in stored_path or stored_path in input_path:
                self.logger.info(f"[FUZZY_DEBUG] Substring match: '{input_path}' <-> '{stored_path}' for {policy_type}")
                return True

            # Strategy 2: Policy-specific keyword matching
            policy_keywords = {
                "privacy_policy": ["privacy", "policy"],
                "terms_and_condition": ["terms", "condition", "service"],
                "shipping_delivery": ["shipping", "delivery", "ship"],
                "contact_us": ["contact", "about"],
                "about_us": ["about", "company"],
                "returns_cancellation": ["return", "cancel", "refund", "exchange"]
            }

            if policy_type in policy_keywords:
                keywords = policy_keywords[policy_type]
                input_has_keywords = any(keyword in input_path.lower() for keyword in keywords)
                stored_has_keywords = any(keyword in stored_path.lower() for keyword in keywords)

                if input_has_keywords and stored_has_keywords:
                    self.logger.info(f"[FUZZY_DEBUG] Keyword match: '{input_path}' <-> '{stored_path}' for {policy_type}")
                    return True

            # Strategy 3: Numeric suffix handling (privacy-policy vs privacy-policy-2)
            input_base = input_path.rstrip('0123456789-')
            stored_base = stored_path.rstrip('0123456789-')

            if input_base == stored_base and len(input_base) > 3:
                self.logger.info(f"[FUZZY_DEBUG] Base path match: '{input_path}' <-> '{stored_path}' for {policy_type}")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error in fuzzy path matching: {e}")
            return False
