{"api_type": "gemini", "timestamp": "2025-07-31T18:50:17.502397", "model_name": "gemini-2.5-flash", "request_id": "test-backup-flow-001", "context": {"service": "orchestrator", "urls": ["home_page"]}, "request": {"prompt": "\nYou are an expert business analyst who has a lot of experience in dealing with business's official website and the detailed information present in websites URLs. \nYou will be provided with 1 URL from a website, and you need to extract specific business information from the content presen in the URLs.\n\n\n* Input Starts *\nURL to analyze:\n- Home_Page Page: https://www.houseofaks.in/\n* Input Ends *\n\n\n* Tasks Starts *\nYou will be company/business policy related URLs, they can contain information regarding Terms and conditions, privacy policy, shipping cancellation and return policy, company contact information etc.\nYour main task is to extract the following information from these URLs.\n\n1. \"legal_name\" ---> Legal Name of the Company: a string, The official registered business name (single value only)\n2. \"business_email\" ---> Business Official Email Address: a list [], Primary business contact email/emails as array (contact@, info@, etc.)\n3. \"support_email\" ---> Support Emails: a list [], Customer support email/emails addresses as array (support@, help@, customerservice@, etc.)\n4. \"business_contact_numbers\" ---> Business Official Contact Numbers: a list[],  Official phone number/numbers for business inquiries as array, extract as it is\n5. \"business_location\" ---> Business Location: a list [], Complete business addresses as array (include all locations if multiple official address is present)\n6. \"accepts_international_orders\" ---> International Shipping Offered : a string, yes or no or not_mentioned, Does the company accept international orders/shipping? (yes/no/not_mentioned). It is assumed that website offered services to Indin. Any country outside India is assumed be international.\n7. \"shipping_countries\" ---> Shipping Countries : a list [], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies\n8. \"shipping_policy_details\" ---> Shipping Policy Details: a string, Summary of phrases or snippets which were used to conclude the answer on international shipping details, (not more than 10-15 words)\n9. \"has_jurisdiction_law\" ---> Jurisdiction/Governing Law: a string, yes/no, Does the website specify governing law or jurisdiction in case of any dispute (yes/no)\n10. \"jurisdiction_place\" ---> Jurisdiction/Governing Law location : a list [], this should have the places of which the governing law applies to any disputes\n11. \"jurisdiction_details\" ---> Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/lar related query above, in not more than 10-15 words.\n\n**Instructions:**\n- Visit this URL and carefully examine the content\n- Visit all the URLs and only derive mentioned in the websites, for e.g. do not assume anything, do not read reviews mentioned, or comments in the websites mentioned to extract any official information. \n- Extract only verified information that appears on the website from valid URLs.\n- If information appears on multiple pages, use the most authoritative source or URLs. \n- If a piece of information is not found, return null for that field\n- Be precise and avoid making assumptions\n- Home page can also have many information. If there is no dedicated URLs or if infomration is not found on the URLs, it can be present in home page as well.\n\n**Output Format:**\nReturn your response strictly in JSON format with below 11 keys:\n\nLook at the context in and around the entity to extract any required information.\n\n```json\n{\n    \"legal_name\": \"Single company legal name or null\", a string, provide \"\" empty string in case not identified.\n    \"business_email\": a list, [\"<EMAIL>\", \"<EMAIL>\"], provide empty [] list in case not identified,\n    \"support_email\": a list, [\"<EMAIL>\", \"<EMAIL>\"], provide empty [] list in case not identified,\n    \"business_contact_numbers\": a list, [\"+1234567890\", \"+0987654321\"], provide empty [] list in case not identified,\n    \"business_location\": a list, [\"Address 1\", \"Address 2\"], provide empty [] list in case not identified,\n    \"accepts_international_orders\": \"yes/no\", a string\n    \"shipping_countries\":[\"India\",\"Australia\"....], a list of destination where the company offers its shipping services, should be specifically mentioned in the policies, provide empty [] list in case not identified,\n    \"shipping_policy_details\": \"Summary of shipping and delivery policies or null\", a string.\n    \"has_jurisdiction_law\": \"yes/no\", Only yes and no, a string.\n    \"jurisdiction_place\": a list [], this should have the places of which the governing law applies to any disputes, provide empty [] list in case not identified,\n    \"jurisdiction_details\" : Jurisdiction/Governing Law details : a string, summary of snippet which was used to specify the jurisdiction/lar related query above, in not more than 10-15 words.\n    }\n\n```\n\nPlease ensure accuracy and only extract information that is clearly visible on the website pages.\n\n\nIMPORTANT: For each policy, analyze only the first 10000 words and the last 10000 words. Also, extract whether shipment/international shipping is accepted (yes/no)."}, "response": "```json\n{\n    \"legal_name\": \"House of AKS\",\n    \"business_email\": [],\n    \"support_email\": [],\n    \"business_contact_numbers\": [],\n    \"business_location\": [],\n    \"accepts_international_orders\": \"yes\",\n    \"shipping_countries\": [\n        \"India\",\n        \"Åland Islands\",\n        \"Albania\",\n        \"Andorra\",\n        \"Australia\",\n        \"Austria\",\n        \"Belarus\",\n        \"Belgium\",\n        \"Bosnia & Herzegovina\",\n        \"Bulgaria\",\n        \"Canada\",\n        \"Croatia\",\n        \"Czechia\",\n        \"Denmark\",\n        \"Estonia\",\n        \"Faroe Islands\",\n        \"Finland\",\n        \"France\",\n        \"Germany\",\n        \"Gibraltar\",\n        \"Greece\",\n        \"Guernsey\",\n        \"Hong Kong SAR\",\n        \"Hungary\",\n        \"Iceland\",\n        \"Ireland\",\n        \"Isle of Man\",\n        \"Israel\",\n        \"Italy\",\n        \"Japan\",\n        \"Jersey\",\n        \"Kosovo\",\n        \"Latvia\",\n        \"Liechtenstein\",\n        \"Lithuania\",\n        \"Luxembourg\",\n        \"Malaysia\",\n        \"Malta\",\n        \"Moldova\",\n        \"Monaco\",\n        \"Montenegro\",\n        \"Netherlands\",\n        \"New Zealand\",\n        \"North Macedonia\",\n        \"Norway\",\n        \"Poland\",\n        \"Portugal\",\n        \"Romania\",\n        \"Russia\",\n        \"San Marino\",\n        \"Serbia\",\n        \"Singapore\",\n        \"Slovakia\",\n        \"Slovenia\",\n        \"South Korea\",\n        \"Spain\",\n        \"Svalbard & Jan Mayen\",\n        \"Sweden\",\n        \"Switzerland\",\n        \"Ukraine\",\n        \"United Arab Emirates\",\n        \"United Kingdom\",\n        \"United States\",\n        \"Vatican City\"\n    ],\n    \"shipping_policy_details\": \"Free Express Shipping above ₹1999. Free international shipping on order above 185$.\",\n    \"has_jurisdiction_law\": \"no\",\n    \"jurisdiction_place\": [],\n    \"jurisdiction_details\": \"\"\n}\n```"}