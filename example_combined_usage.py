#!/usr/bin/env python3
"""
Example usage of the Combined Entity Extraction Service
"""

import sys
import os
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.Extractor.services.combined_entity_extraction_service import CombinedEntityExtractionService
from app.Extractor.services.url_data_retrieval import UrlDataRetrievalService


def example_direct_usage():
    """
    Example of using the CombinedEntityExtractionService directly
    """
    print("Combined Entity Extraction Service - Direct Usage Example")
    print("=" * 60)
    
    # Initialize the service
    combined_service = CombinedEntityExtractionService()
    
    # Example website URL (you can change this)
    website_url = "https://example.com"
    org_id = "default"
    
    print(f"Website: {website_url}")
    print(f"Organization ID: {org_id}")
    
    # First, we need to find the scrape_request_ref_id
    print("\n1. Finding scrape request reference ID...")
    try:
        scrape_request_ref_id = UrlDataRetrievalService.find_scrape_request_ref_id_by_website(
            website_url, org_id
        )
        
        if not scrape_request_ref_id:
            print(f"❌ No scrape request found for {website_url}")
            print("Please ensure the website has been scraped and classified first.")
            return
        
        print(f"✅ Found scrape request ID: {scrape_request_ref_id}")
        
    except Exception as e:
        print(f"❌ Error finding scrape request: {str(e)}")
        return
    
    # Now run the combined extraction
    print("\n2. Running combined entity extraction...")
    try:
        result = combined_service.extract_entities_combined(
            scrape_request_ref_id=scrape_request_ref_id,
            website_url=website_url
        )
        
        if result.get("success", False):
            print("✅ Combined extraction completed successfully!")
            
            # Display summary
            print(f"\nSummary:")
            print(f"  Total fields extracted: {result.get('total_fields_extracted', 0)}")
            print(f"  Missing fields from Gemini: {result.get('missing_fields_count', 0)}")
            print(f"  Timestamp: {result.get('timestamp', 'N/A')}")
            
            # Show field sources
            field_sources = result.get('field_sources', {})
            gemini_fields = [k for k, v in field_sources.items() if v == 'gemini']
            openai_fields = [k for k, v in field_sources.items() if v == 'openai']
            not_found_fields = [k for k, v in field_sources.items() if v == 'not_found']
            
            print(f"\nField Sources:")
            print(f"  From Gemini ({len(gemini_fields)}): {gemini_fields}")
            print(f"  From OpenAI ({len(openai_fields)}): {openai_fields}")
            print(f"  Not found ({len(not_found_fields)}): {not_found_fields}")
            
            # Show detailed results
            print(f"\nDetailed Results:")
            combined_result = result.get('combined_result', {})
            for field, value in combined_result.items():
                source = field_sources.get(field, 'unknown')
                if value and value != "":
                    print(f"  ✅ {field}: {value} (from {source})")
                else:
                    print(f"  ❌ {field}: [not found] (from {source})")
            
            # Save results to file
            output_file = f"combined_extraction_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(output_file, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"\n💾 Full results saved to: {output_file}")
            
        else:
            print(f"❌ Combined extraction failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during extraction: {str(e)}")
        import traceback
        traceback.print_exc()


def example_api_comparison():
    """
    Example showing comparison between individual API calls and combined approach
    """
    print("\nAPI Comparison Example")
    print("=" * 40)
    
    # This would show how the combined approach compares to individual API calls
    # For now, just show the concept
    
    print("Individual API approach:")
    print("  1. Call Gemini API")
    print("  2. Parse results")
    print("  3. Identify missing fields manually")
    print("  4. Call OpenAI API for missing fields")
    print("  5. Merge results manually")
    
    print("\nCombined approach:")
    print("  1. Call combined_service.extract_entities_combined()")
    print("  2. Get complete results with field sources")
    print("  3. Automatic null detection and fallback")
    print("  4. Comprehensive logging and error handling")


def main():
    """Main example function"""
    print("Combined Entity Extraction Service Examples")
    print("=" * 60)
    
    # Check if a website URL was provided as command line argument
    if len(sys.argv) > 1:
        website_url = sys.argv[1]
        print(f"Using website from command line: {website_url}")
        # You could modify the example to use this URL
    
    try:
        # Run the direct usage example
        example_direct_usage()
        
        # Show API comparison
        example_api_comparison()
        
    except Exception as e:
        print(f"❌ Error running examples: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("Examples completed!")
    print("\nTo use with a specific website:")
    print(f"python {__file__} https://your-website.com")


if __name__ == "__main__":
    main()
