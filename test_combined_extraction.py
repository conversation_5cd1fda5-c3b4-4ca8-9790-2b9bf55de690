#!/usr/bin/env python3
"""
Test script for the combined entity extraction service
"""

import requests
import json
import sys
from typing import Dict, Any


def test_combined_extraction_api(website_url: str, base_url: str = "http://localhost:8000") -> Dict[str, Any]:
    """
    Test the combined entity extraction API endpoint
    
    Args:
        website_url: Website URL to analyze
        base_url: Base URL of the API server
        
    Returns:
        API response
    """
    endpoint = f"{base_url}/entity-extraction/analyze-combined"
    
    request_data = {
        "website_url": website_url,
        "org_id": "default",
        "force_reprocess": False,
        "use_openai_fallback": True
    }
    
    print(f"Testing combined extraction for: {website_url}")
    print(f"Endpoint: {endpoint}")
    print(f"Request data: {json.dumps(request_data, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(endpoint, json=request_data, timeout=300)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS!")
            print(f"Total fields extracted: {result.get('total_fields_extracted', 0)}")
            print(f"Missing fields from Gemini: {result.get('missing_fields_count', 0)}")
            
            # Show field sources
            field_sources = result.get('field_sources', {})
            gemini_fields = [k for k, v in field_sources.items() if v == 'gemini']
            openai_fields = [k for k, v in field_sources.items() if v == 'openai']
            not_found_fields = [k for k, v in field_sources.items() if v == 'not_found']
            
            print(f"\nFields from Gemini ({len(gemini_fields)}): {gemini_fields}")
            print(f"Fields from OpenAI ({len(openai_fields)}): {openai_fields}")
            print(f"Fields not found ({len(not_found_fields)}): {not_found_fields}")
            
            # Show combined results
            combined_result = result.get('combined_result', {})
            print(f"\nCombined Results:")
            for field, value in combined_result.items():
                source = field_sources.get(field, 'unknown')
                print(f"  {field}: {value} (from {source})")
            
            return result
            
        else:
            print(f"❌ ERROR: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error details: {json.dumps(error_detail, indent=2)}")
            except:
                print(f"Error text: {response.text}")
            return {"error": f"HTTP {response.status_code}"}
            
    except requests.exceptions.RequestException as e:
        print(f"❌ REQUEST ERROR: {str(e)}")
        return {"error": f"Request failed: {str(e)}"}
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {str(e)}")
        return {"error": f"Unexpected error: {str(e)}"}


def test_health_check(base_url: str = "http://localhost:8000") -> bool:
    """Test if the API server is running"""
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ API server is running")
            return True
        else:
            print(f"❌ API server returned {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API server: {str(e)}")
        return False


def main():
    """Main test function"""
    print("Combined Entity Extraction Test")
    print("=" * 50)
    
    # Check if server is running
    if not test_health_check():
        print("\nPlease start the API server first:")
        print("uvicorn app.main:app --reload")
        sys.exit(1)
    
    # Test websites (you can modify these)
    test_websites = [
        "https://example.com",
        # Add more test websites here
    ]
    
    if len(sys.argv) > 1:
        # Use website from command line argument
        test_websites = [sys.argv[1]]
    
    for website in test_websites:
        print(f"\n{'='*60}")
        result = test_combined_extraction_api(website)
        
        if "error" not in result:
            print(f"\n✅ Test completed successfully for {website}")
        else:
            print(f"\n❌ Test failed for {website}: {result['error']}")
        
        print("="*60)
    
    print("\nTest completed!")


if __name__ == "__main__":
    main()
