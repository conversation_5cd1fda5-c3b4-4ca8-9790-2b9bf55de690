#!/usr/bin/env python3
"""
Batch Testing Script for Enhanced Entity Extraction Backup Flow

This script tests the enhanced multiple OpenAI calls backup flow by:
1. Getting test data from the database
2. Making API calls to entity extraction endpoint
3. Analyzing extraction success rates
4. Generating comprehensive reports
"""

import requests
import json
import time
import pymysql
import os
from dotenv import load_dotenv
from datetime import datetime
from typing import List, Dict, Any
import pandas as pd

# Load environment variables
load_dotenv('.env')

class EntityExtractionBatchTester:
    def __init__(self):
        self.api_base_url = "http://localhost:8000"
        self.results = []
        self.db_connection = None
        
        # Fields to track for analysis
        self.tracked_fields = [
            'legal_name', 'business_email', 'support_email', 
            'business_contact_numbers', 'business_location',
            'has_jurisdiction_law', 'jurisdiction_details', 'jurisdiction_place',
            'accepts_international_orders', 'shipping_countries', 'shipping_policy_details'
        ]
    
    def connect_to_database(self):
        """Connect to the database"""
        try:
            db_url = os.getenv('DATABASE_URL', '')
            
            if 'mysql' in db_url:
                import re
                match = re.match(r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
                if match:
                    user, password, host, port, database = match.groups()
                    
                    self.db_connection = pymysql.connect(
                        host=host,
                        port=int(port),
                        user=user,
                        password=password,
                        database=database
                    )
                    print("✅ Connected to database successfully")
                    return True
                else:
                    print("❌ Could not parse database URL")
                    return False
            else:
                print("❌ Not a MySQL database URL")
                return False
                
        except Exception as e:
            print(f"❌ Error connecting to database: {e}")
            return False
    
    def get_test_scrape_ids(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get test scrape IDs from database with policy data info"""
        try:
            with self.db_connection.cursor() as cursor:
                query = """
                    SELECT p.scrape_request_ref_id, p.id,
                           CASE WHEN p.privacy_policy_text IS NOT NULL AND p.privacy_policy_text != 'not_applicable' AND LENGTH(p.privacy_policy_text) > 100 THEN 1 ELSE 0 END as has_privacy,
                           CASE WHEN p.terms_and_condition_text IS NOT NULL AND p.terms_and_condition_text != 'not_applicable' AND LENGTH(p.terms_and_condition_text) > 100 THEN 1 ELSE 0 END as has_terms,
                           CASE WHEN p.contact_us_text IS NOT NULL AND p.contact_us_text != 'not_applicable' AND LENGTH(p.contact_us_text) > 100 THEN 1 ELSE 0 END as has_contact,
                           CASE WHEN p.shipping_delivery_text IS NOT NULL AND p.shipping_delivery_text != 'not_applicable' AND LENGTH(p.shipping_delivery_text) > 100 THEN 1 ELSE 0 END as has_shipping,
                           CASE WHEN p.about_us_text IS NOT NULL AND p.about_us_text != 'not_applicable' AND LENGTH(p.about_us_text) > 100 THEN 1 ELSE 0 END as has_about,
                           CASE WHEN p.home_page_text IS NOT NULL AND p.home_page_text != 'not_applicable' AND LENGTH(p.home_page_text) > 100 THEN 1 ELSE 0 END as has_home
                    FROM policy_analysis_new_gemini p
                    WHERE p.scrape_request_ref_id IS NOT NULL 
                    ORDER BY p.id DESC 
                    LIMIT %s
                """
                
                cursor.execute(query, (limit,))
                rows = cursor.fetchall()
                
                test_data = []
                for row in rows:
                    scrape_id, record_id, privacy, terms, contact, shipping, about, home = row
                    total_policies = privacy + terms + contact + shipping + about + home
                    
                    test_data.append({
                        'scrape_request_ref_id': scrape_id,
                        'record_id': record_id,
                        'policy_count': total_policies,
                        'has_privacy': bool(privacy),
                        'has_terms': bool(terms),
                        'has_contact': bool(contact),
                        'has_shipping': bool(shipping),
                        'has_about': bool(about),
                        'has_home': bool(home)
                    })
                
                print(f"✅ Retrieved {len(test_data)} test scrape IDs")
                return test_data
                
        except Exception as e:
            print(f"❌ Error getting test data: {e}")
            return []
    
    def make_entity_extraction_call(self, scrape_request_ref_id: str) -> Dict[str, Any]:
        """Make API call to entity extraction endpoint"""
        try:
            url = f"{self.api_base_url}/entity-extraction/analyze"
            payload = {
                "scrape_request_ref_id": scrape_request_ref_id,
                "website_url": "https://example.com/",
                "org_id": "default"
            }
            
            headers = {
                "accept": "application/json",
                "Content-Type": "application/json"
            }
            
            print(f"🔄 Testing {scrape_request_ref_id}...")
            start_time = time.time()
            
            response = requests.post(url, json=payload, headers=headers, timeout=120)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                result['processing_time'] = processing_time
                print(f"✅ Success in {processing_time:.1f}s - Method: {result.get('extraction_method', 'unknown')}")
                return result
            else:
                print(f"❌ API Error {response.status_code}: {response.text}")
                return {
                    'error': f"API Error {response.status_code}",
                    'processing_time': processing_time,
                    'scrape_request_ref_id': scrape_request_ref_id
                }
                
        except Exception as e:
            print(f"❌ Exception during API call: {e}")
            return {
                'error': str(e),
                'scrape_request_ref_id': scrape_request_ref_id
            }
    
    def analyze_extraction_result(self, result: Dict[str, Any], test_info: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the extraction result for completeness"""
        # Handle urls_processed safely
        urls_processed = result.get('urls_processed', [])
        if urls_processed is None:
            urls_processed = []

        analysis = {
            'scrape_request_ref_id': result.get('scrape_request_ref_id'),
            'policy_count': test_info['policy_count'],
            'extraction_method': result.get('extraction_method'),
            'processing_time': result.get('processing_time', 0),
            'urls_processed_count': len(urls_processed),
            'error': result.get('error'),
            'field_analysis': {},
            'total_fields': len(self.tracked_fields),
            'extracted_fields': 0,
            'null_fields': 0,
            'extraction_rate': 0.0
        }
        
        # Analyze each tracked field
        for field in self.tracked_fields:
            field_value = result.get(field)
            is_extracted = field_value is not None and field_value != "" and field_value != "null"
            
            analysis['field_analysis'][field] = {
                'value': field_value,
                'extracted': is_extracted,
                'length': len(str(field_value)) if field_value else 0
            }
            
            if is_extracted:
                analysis['extracted_fields'] += 1
            else:
                analysis['null_fields'] += 1
        
        analysis['extraction_rate'] = (analysis['extracted_fields'] / analysis['total_fields']) * 100
        
        return analysis
    
    def run_batch_test(self, limit: int = 20):
        """Run the complete batch test"""
        print("🚀 Starting Entity Extraction Batch Test")
        print("=" * 60)
        
        # Connect to database
        if not self.connect_to_database():
            return
        
        # Get test data
        test_data = self.get_test_scrape_ids(limit)
        if not test_data:
            print("❌ No test data available")
            return
        
        print(f"\n📊 Testing {len(test_data)} scrape IDs...")
        print("=" * 60)
        
        # Process each test case
        for i, test_info in enumerate(test_data, 1):
            scrape_id = test_info['scrape_request_ref_id']
            print(f"\n[{i}/{len(test_data)}] Testing: {scrape_id}")
            print(f"Policy Count: {test_info['policy_count']}")
            
            # Make API call
            result = self.make_entity_extraction_call(scrape_id)
            
            # Analyze result
            analysis = self.analyze_extraction_result(result, test_info)
            self.results.append(analysis)
            
            # Brief result summary
            if not analysis['error']:
                print(f"📈 Extraction Rate: {analysis['extraction_rate']:.1f}% ({analysis['extracted_fields']}/{analysis['total_fields']})")
            
            # Small delay between requests
            time.sleep(1)
        
        # Generate final report
        self.generate_report()
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 80)
        print("📊 BATCH TEST RESULTS REPORT")
        print("=" * 80)
        
        if not self.results:
            print("❌ No results to analyze")
            return
        
        # Filter successful results
        successful_results = [r for r in self.results if not r.get('error')]
        failed_results = [r for r in self.results if r.get('error')]
        
        print(f"\n📈 OVERALL STATISTICS:")
        print(f"Total Tests: {len(self.results)}")
        print(f"Successful: {len(successful_results)}")
        print(f"Failed: {len(failed_results)}")
        print(f"Success Rate: {(len(successful_results)/len(self.results)*100):.1f}%")
        
        if successful_results:
            # Extraction method analysis
            method_counts = {}
            for result in successful_results:
                method = result['extraction_method']
                method_counts[method] = method_counts.get(method, 0) + 1
            
            print(f"\n🔧 EXTRACTION METHODS:")
            for method, count in method_counts.items():
                percentage = (count / len(successful_results)) * 100
                print(f"  {method}: {count} ({percentage:.1f}%)")
            
            # Field extraction analysis
            field_stats = {}
            for field in self.tracked_fields:
                extracted_count = sum(1 for r in successful_results if r['field_analysis'][field]['extracted'])
                field_stats[field] = {
                    'extracted': extracted_count,
                    'rate': (extracted_count / len(successful_results)) * 100
                }
            
            print(f"\n📋 FIELD EXTRACTION RATES:")
            for field, stats in sorted(field_stats.items(), key=lambda x: x[1]['rate'], reverse=True):
                print(f"  {field}: {stats['extracted']}/{len(successful_results)} ({stats['rate']:.1f}%)")
            
            # Overall extraction rate
            total_extraction_rate = sum(r['extraction_rate'] for r in successful_results) / len(successful_results)
            print(f"\n🎯 AVERAGE EXTRACTION RATE: {total_extraction_rate:.1f}%")
            
            # Processing time analysis
            avg_processing_time = sum(r['processing_time'] for r in successful_results) / len(successful_results)
            print(f"⏱️  AVERAGE PROCESSING TIME: {avg_processing_time:.1f}s")
            
            # Policy count vs extraction rate correlation
            print(f"\n📚 POLICY COUNT vs EXTRACTION RATE:")
            policy_groups = {}
            for result in successful_results:
                policy_count = result['policy_count']
                if policy_count not in policy_groups:
                    policy_groups[policy_count] = []
                policy_groups[policy_count].append(result['extraction_rate'])
            
            for policy_count in sorted(policy_groups.keys()):
                rates = policy_groups[policy_count]
                avg_rate = sum(rates) / len(rates)
                print(f"  {policy_count} policies: {len(rates)} tests, {avg_rate:.1f}% avg extraction")
        
        # Save detailed results to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"batch_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        print(f"\n💾 Detailed results saved to: {filename}")
        print("=" * 80)

if __name__ == "__main__":
    tester = EntityExtractionBatchTester()
    tester.run_batch_test(limit=20)
